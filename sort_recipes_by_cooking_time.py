#!/usr/bin/env python3
"""
Recipe Sorting Script

This script analyzes recipe markdown files in the recipes folder and sorts them
by active cooking time, outputting the results to a YAML file.
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple


def parse_cooking_time(time_str: str) -> int:
    """
    Parse cooking time string and return minutes as integer.
    
    Args:
        time_str: String like "10 minutes", "1 hour", "1.5 hours", etc.
    
    Returns:
        Total minutes as integer
    """
    if not time_str:
        return 0
    
    # Remove extra whitespace and convert to lowercase
    time_str = time_str.strip().lower()
    
    # Extract numbers (including decimals)
    numbers = re.findall(r'\d+\.?\d*', time_str)
    if not numbers:
        return 0
    
    total_minutes = 0
    
    # Handle hours
    if 'hour' in time_str:
        hours = float(numbers[0])
        total_minutes += int(hours * 60)
        # If there are additional numbers, they might be minutes
        if len(numbers) > 1 and 'min' in time_str:
            total_minutes += int(float(numbers[1]))
    # Handle minutes only
    elif 'min' in time_str:
        total_minutes = int(float(numbers[0]))
    else:
        # Default to minutes if no unit specified
        total_minutes = int(float(numbers[0]))
    
    return total_minutes


def extract_tags_from_line(line: str) -> List[str]:
    """
    Extract hashtags from a line.
    
    Args:
        line: Line that may contain hashtags like "#Vegetables #Protein"
    
    Returns:
        List of tag names (without the # symbol)
    """
    tags = re.findall(r'#(\w+)', line)
    return tags


def categorize_recipe(tags: List[str]) -> str:
    """
    Categorize recipe based on tags into vegetables, protein, or vegetables_protein.
    
    Args:
        tags: List of tag strings
    
    Returns:
        Category string: "vegetables", "protein", or "vegetables_protein"
    """
    tags_lower = [tag.lower() for tag in tags]
    
    has_vegetables = any('vegetable' in tag for tag in tags_lower)
    has_protein = any('protein' in tag for tag in tags_lower)
    has_vegetables_protein = any('vegetables_protein' in tag for tag in tags_lower)
    
    if has_vegetables_protein:
        return "vegetables_protein"
    elif has_vegetables and has_protein:
        return "vegetables_protein"
    elif has_protein:
        return "protein"
    elif has_vegetables:
        return "vegetables"
    else:
        # Default categorization based on common ingredients if no clear tags
        return "vegetables"  # Default fallback


def parse_recipe_file(file_path: Path) -> Optional[Dict]:
    """
    Parse a single recipe markdown file.
    
    Args:
        file_path: Path to the recipe file
    
    Returns:
        Dictionary with recipe information or None if parsing fails
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Extract recipe name from first line (assuming it's a markdown header)
        recipe_name = ""
        tags = []
        active_time = 0
        total_time = 0
        
        for i, line in enumerate(lines):
            line = line.strip()
            
            # Extract recipe name from first header
            if line.startswith('# ') and not recipe_name:
                recipe_name = line[2:].strip()
            
            # Extract tags (lines starting with #)
            elif line.startswith('#') and not line.startswith('# ') and not line.startswith('## '):
                tags.extend(extract_tags_from_line(line))
            
            # Extract cooking times
            elif 'active cooking time:' in line.lower():
                time_match = re.search(r'active cooking time:\s*(.+)', line, re.IGNORECASE)
                if time_match:
                    active_time = parse_cooking_time(time_match.group(1))
            
            elif ('total cooking time:' in line.lower() or 
                  'ready in:' in line.lower()):
                # Handle both "Total Cooking Time:" and "Ready in:" formats
                time_match = re.search(r'(?:total cooking time|ready in):\s*(.+)', line, re.IGNORECASE)
                if time_match:
                    time_str = time_match.group(1)
                    # Remove parenthetical information like "(plus overnight marination)"
                    time_str = re.sub(r'\s*\([^)]+\)', '', time_str)
                    total_time = parse_cooking_time(time_str)
        
        if not recipe_name:
            recipe_name = file_path.stem.replace('_', ' ').title()
        
        category = categorize_recipe(tags)
        
        return {
            'name': recipe_name,
            'active_cooking_time_minutes': active_time,
            'total_cooking_time_minutes': total_time,
            'category': category,
            'tags': tags,
            'file_path': str(file_path.relative_to(Path.cwd()))
        }
    
    except Exception as e:
        print(f"Error parsing {file_path}: {e}")
        return None


def find_recipe_files(recipes_dir: Path) -> List[Path]:
    """
    Find all markdown recipe files in the recipes directory.
    
    Args:
        recipes_dir: Path to the recipes directory
    
    Returns:
        List of paths to recipe files
    """
    recipe_files = []
    
    for root, dirs, files in os.walk(recipes_dir):
        for file in files:
            if file.endswith('.md'):
                recipe_files.append(Path(root) / file)
    
    return recipe_files


def main():
    """Main function to process recipes and generate sorted YAML output."""
    
    # Define paths
    recipes_dir = Path('recipes')
    output_file = Path('sorted_recipes_by_cooking_time.yaml')
    
    if not recipes_dir.exists():
        print(f"Error: Recipes directory '{recipes_dir}' not found!")
        return
    
    # Find all recipe files
    recipe_files = find_recipe_files(recipes_dir)
    print(f"Found {len(recipe_files)} recipe files")
    
    # Parse all recipes
    recipes = []
    for file_path in recipe_files:
        recipe_data = parse_recipe_file(file_path)
        if recipe_data:
            recipes.append(recipe_data)
            print(f"Parsed: {recipe_data['name']} - Active: {recipe_data['active_cooking_time_minutes']}min")
    
    # Sort recipes by active cooking time
    recipes.sort(key=lambda x: x['active_cooking_time_minutes'])
    
    # Prepare output data
    output_data = {
        'sorted_recipes_by_active_cooking_time': recipes,
        'summary': {
            'total_recipes': len(recipes),
            'categories': {
                'vegetables': len([r for r in recipes if r['category'] == 'vegetables']),
                'protein': len([r for r in recipes if r['category'] == 'protein']),
                'vegetables_protein': len([r for r in recipes if r['category'] == 'vegetables_protein'])
            },
            'active_cooking_time_range': {
                'min_minutes': min(r['active_cooking_time_minutes'] for r in recipes) if recipes else 0,
                'max_minutes': max(r['active_cooking_time_minutes'] for r in recipes) if recipes else 0
            }
        }
    }
    
    # Write to YAML file
    with open(output_file, 'w', encoding='utf-8') as f:
        # Create YAML-like output manually since PyYAML is not available
        f.write("sorted_recipes_by_active_cooking_time:\n")
        for recipe in recipes:
            f.write(f"  - name: \"{recipe['name']}\"\n")
            f.write(f"    active_cooking_time_minutes: {recipe['active_cooking_time_minutes']}\n")
            f.write(f"    total_cooking_time_minutes: {recipe['total_cooking_time_minutes']}\n")
            f.write(f"    category: {recipe['category']}\n")
            f.write(f"    tags: {recipe['tags']}\n")
            f.write(f"    file_path: \"{recipe['file_path']}\"\n")

        f.write("\nsummary:\n")
        f.write(f"  total_recipes: {output_data['summary']['total_recipes']}\n")
        f.write("  categories:\n")
        for category, count in output_data['summary']['categories'].items():
            f.write(f"    {category}: {count}\n")
        f.write("  active_cooking_time_range:\n")
        f.write(f"    min_minutes: {output_data['summary']['active_cooking_time_range']['min_minutes']}\n")
        f.write(f"    max_minutes: {output_data['summary']['active_cooking_time_range']['max_minutes']}\n")
    
    print(f"\nSorted recipes saved to: {output_file}")
    print(f"Total recipes processed: {len(recipes)}")
    print("\nCategory breakdown:")
    for category, count in output_data['summary']['categories'].items():
        print(f"  {category}: {count}")


if __name__ == "__main__":
    main()
