# BusyBee Recipes

Recipe book for people who are crazily busy but still want to eat healthy home-cooked food:
- Simple
- Easy to use
- Short active cooking time, i.e., less time a person actually spends in the kitchen
- Promote use of efficiency tools in the kitchen
- Multicultural and diverse

For tech-savvy users, it can also be interactive with the help of Gemini CLI. All recipes are written in English, but Gemini CLI can translate the recipes to other languages and other unit systems.

## Advanced Features

### Menu Planning and Shopping List Generation

To come soon.

### Search, Selection, and Filtering with Gemini CLI

This repo contains a GEMINI.md file that configures the Gemini CLI to work more finely with this repo. You need to [install the Gemini CLI](https://github.com/google-gemini/gemini-cli/blob/main/README.md) to use it.